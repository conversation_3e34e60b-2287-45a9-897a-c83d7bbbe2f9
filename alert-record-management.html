<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警记录管理</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 日期选择器 -->
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
    <style>
        .alert-status-0 { color: #dc3545; font-weight: bold; }
        .alert-status-1 { color: #28a745; }
        .dispose-status-0 { color: #ffc107; font-weight: bold; }
        .dispose-status-1 { color: #28a745; }
        .table-responsive { max-height: 600px; overflow-y: auto; }
        .btn-group-sm .btn { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
        .search-form { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .status-open { background-color: #fff3cd; color: #856404; }
        .status-closed { background-color: #d1ecf1; color: #0c5460; }
        .status-undisposed { background-color: #f8d7da; color: #721c24; }
        .status-disposed { background-color: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-exclamation-triangle text-warning"></i> 预警记录管理</h2>
                
                <!-- 查询条件 -->
                <div class="search-form">
                    <form id="searchForm">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">预警事项编码</label>
                                <input type="text" class="form-control" id="itemCode" placeholder="请输入事项编码">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">预警对象ID</label>
                                <input type="text" class="form-control" id="alertObjectId" placeholder="请输入对象ID">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">预警状态</label>
                                <select class="form-select" id="alertStatus">
                                    <option value="">全部</option>
                                    <option value="0">未关闭</option>
                                    <option value="1">已关闭</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">处置状态</label>
                                <select class="form-select" id="disposeStatus">
                                    <option value="">全部</option>
                                    <option value="0">未处置</option>
                                    <option value="1">已处置</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">预警时间(开始)</label>
                                <input type="text" class="form-control" id="alertTimeStart" placeholder="选择开始时间">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">预警时间(结束)</label>
                                <input type="text" class="form-control" id="alertTimeEnd" placeholder="选择结束时间">
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="button" class="btn btn-primary me-2" onclick="searchRecords()">
                                    <i class="fas fa-search"></i> 查询
                                </button>
                                <button type="button" class="btn btn-secondary me-2" onclick="resetSearch()">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                                <button type="button" class="btn btn-success" onclick="exportRecords()">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 统计信息 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary" id="totalCount">0</h5>
                                <p class="card-text">总记录数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger" id="openCount">0</h5>
                                <p class="card-text">未关闭</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning" id="undisposedCount">0</h5>
                                <p class="card-text">未处置</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success" id="disposedCount">0</h5>
                                <p class="card-text">已处置</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">预警记录列表</h5>
                        <div>
                            <button type="button" class="btn btn-warning btn-sm" onclick="batchDispose()">
                                <i class="fas fa-tasks"></i> 批量处置
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="batchClose()">
                                <i class="fas fa-times-circle"></i> 批量关闭
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                        </th>
                                        <th>事项编码</th>
                                        <th>预警对象ID</th>
                                        <th>预警时间</th>
                                        <th>事项描述</th>
                                        <th>预警状态</th>
                                        <th>处置状态</th>
                                        <th>处置人</th>
                                        <th>处置时间</th>
                                        <th width="200">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="recordTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <nav>
                            <ul class="pagination justify-content-center mb-0" id="pagination">
                                <!-- 分页将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 处置模态框 -->
    <div class="modal fade" id="disposeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">预警记录处置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="disposeForm">
                        <input type="hidden" id="disposeRecordId">
                        <div class="mb-3">
                            <label class="form-label">处置结果 <span class="text-danger">*</span></label>
                            <select class="form-select" id="disposeResultCode" required>
                                <option value="">请选择处置结果</option>
                                <option value="1001">已解决</option>
                                <option value="1002">转交处理</option>
                                <option value="1003">暂缓处理</option>
                                <option value="1004">无需处理</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">处置说明 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="disposeReason" rows="4" required 
                                      placeholder="请详细说明处置情况..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitDispose()">确认处置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 关闭模态框 -->
    <div class="modal fade" id="closeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">关闭预警记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="closeForm">
                        <input type="hidden" id="closeRecordId">
                        <div class="mb-3">
                            <label class="form-label">关闭原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="closeReason" rows="4" required 
                                      placeholder="请说明关闭原因..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="submitClose()">确认关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">预警记录详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="detailContent">
                    <!-- 详情内容将通过JavaScript动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>
